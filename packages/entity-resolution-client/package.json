{"name": "@bybeam/entity-resolution-client", "version": "0.0.0", "description": "Typescript client for the entity resolution service", "private": true, "main": "dist/index.js", "scripts": {"build": "tsc --build", "generate": "sh ./scripts/generate.sh"}, "devDependencies": {"@bybeam/typescript-config": "workspace:*", "typescript": "5.5.4"}, "dependencies": {"@grpc/grpc-js": "1.10.10", "@grpc/proto-loader": "0.7.10", "google-proto-files": "4.0.0", "google-protobuf": "3.21.2"}}