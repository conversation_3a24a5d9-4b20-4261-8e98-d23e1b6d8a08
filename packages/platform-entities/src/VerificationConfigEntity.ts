import { ServiceType, VerificationConfig, VerificationConfigStatus } from "@bybeam/platform-types";
import { Column, CreateDateColumn, DeleteDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";

@Entity('verifcation_configs')
export class VerificationConfigEntity implements VerificationConfig {
  @PrimaryGeneratedColumn('uuid')
  public id: string;

  @Column({ type: 'uuid', name: 'program_id' })
  public programId: string;

  @Column({ type: 'uuid', name: 'applicant_type_id' })
  public applicantTypeId: string;

  @Column({ type: 'text', name: 'service_type' })
  public serviceType: ServiceType;

  @Column({ type: 'jsonb' })
  public config: VerificationConfig['config'];

  @Column({ type: 'text' })
  public status: VerificationConfigStatus;

  @Column({ type: 'text', name: 'filepath' })
  public filepath?: string;

  @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
  public createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
  public updatedAt: Date;

  @Column({ type: 'timestamp', name: 'uploaded_at' })
  public uploadedAt?: Date;

  @DeleteDateColumn({ type: 'timestamp', name: 'deactivated_at' })
  public deactivatedAt?: Date;
}

export default VerificationConfigEntity;
