interface LookupConfigField {
  key: string;
  details: string;
  sample: string;
  weight?: number;
  metadata: boolean;
}

enum ServiceType {
  DataLookup = 'DataLookup',
}

interface UpsertLookupConfigInput {
  id: string;
  fields: LookupConfigField[];
  applicantTypeId: string;
}

interface UploadFileInput {
  id: string;
  applicantTypeId: string;
  service: ServiceType;
  filepath?: string;
}

const USER_FIELDS = ['name', 'email'];

enum VerificationConfigStatus {
  InProgress = 'InProgress',
  Success = 'Success',
  Failed = 'Failed',
}

interface VerificationConfig {
  id: string;
  programId: string;
  applicantTypeId: string;
  serviceType: ServiceType;
  config: { fields: LookupConfigField[] };
  status: VerificationConfigStatus;
  filepath?: string;
  createdAt: Date;
  updatedAt: Date;
  deactivatedAt?: Date;
}

interface UpdateVerificationConfigInput {
  id: string;
  config?: { fields: LookupConfigField[] };
  filepath?: string;
  status?: VerificationConfigStatus;
}

export { ServiceType, USER_FIELDS, VerificationConfigStatus };
export type {
  LookupConfigField,
  VerificationConfig,
  UpsertLookupConfigInput,
  UpdateVerificationConfigInput,
  UploadFileInput,
};