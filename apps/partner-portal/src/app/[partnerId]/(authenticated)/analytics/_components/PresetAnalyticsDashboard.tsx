'use client';
import { usePresetGuestToken } from '@/app/hooks/usePresetGuestToken';
import usePartner from '@/spa-legacy/common/hooks/contexts/usePartner';
import dayjs from '@/spa-legacy/common/utilities/dayjsConfig';
import { useQuery } from '@apollo/client';
import { Partner } from '@bybeam/platform-types';
import { embedDashboard } from '@preset-sdk/embedded';
import { Skeleton, TabNav } from '@radix-ui/themes';
import { env } from 'next-runtime-env';
import { useEffect, useMemo, useRef, useState } from 'react';
import GetPartnerAnalyticsQuery from './GetPartnerAnalyticsResources.graphql';
import { usePostHog } from 'posthog-js/react';
import { POSTHOG_FEATURE_FLAGS } from '@/app/posthog';

interface TokenInfo {
  token: string;
  expiresAt: string;
}

export default function PresetAnalyticsDashboard(): JSX.Element {
  const { externalId } = usePartner();
  const [tokenInfo, setTokenInfo] = useState<TokenInfo>();
  const [error, setError] = useState<string>();
  const { getGuestToken, refreshGuestToken, loading } = usePresetGuestToken();
  const partner = usePartner();
  const standardSupersetUrl = env('NEXT_PUBLIC_SUPERSET_DOMAIN');
  const standardDashboardId = env('NEXT_PUBLIC_PRESET_DASHBOARD_ID');
  const posthog = usePostHog();
  const isStandardReportDisabled = posthog.isFeatureEnabled(
    POSTHOG_FEATURE_FLAGS.presetDisableStandardReport,
  );
  const refreshTimeoutRef = useRef<NodeJS.Timeout>();
  const { data, loading: loadingResources } = useQuery<
    { partners: { partners: Partner[] } },
    { externalId: string }
  >(GetPartnerAnalyticsQuery, { variables: { externalId } });
  const resources = (data?.partners?.partners?.[0]?.analyticsResources || []).filter(
    (resource) => resource.dashboardId,
  );

  const [{ dashboardId, workspaceId, supersetUrl }, setActiveDashboard] = useState<{
    dashboardId: string | undefined;
    workspaceId?: string;
    supersetUrl?: string;
  }>({
    dashboardId: standardDashboardId,
    workspaceId: undefined,
    supersetUrl: standardSupersetUrl,
  });
  const dashboardRef = useRef(dashboardId);

  const hasPresetCredentials = useMemo(
    () => Boolean(supersetUrl && dashboardId && partner?.id),
    [supersetUrl, dashboardId, partner?.id],
  );

  const setupTokenRefresh = (expiresAt: string): void => {
    if (refreshTimeoutRef.current) {
      clearTimeout(refreshTimeoutRef.current);
    }

    const refreshTime = dayjs(expiresAt).subtract(1, 'minute');
    const timeUntilRefresh = refreshTime.valueOf() - dayjs().valueOf();

    // Only set up refresh if it's in the future
    if (timeUntilRefresh > 0) {
      refreshTimeoutRef.current = setTimeout(refresh, timeUntilRefresh);
    } else {
      console.error('token expired');
      refreshTimeoutRef.current = setTimeout(refresh, 1000);
    }

    async function refresh() {
      try {
        if (!partner?.id || !dashboardId) return;

        const newTokenInfo = await refreshGuestToken({
          partnerId: partner.id,
          dashboardId,
        });
        setTokenInfo(newTokenInfo);
        setupTokenRefresh(newTokenInfo.expiresAt);
      } catch (err) {
        console.error('Failed to refresh token:', err);
        setError(err instanceof Error ? err.message : 'Failed to refresh token');
      }
    }
  };

  useEffect(() => {
    if (dashboardRef.current !== dashboardId) {
      dashboardRef.current = dashboardId;
      fetchToken();
    }
    if (tokenInfo) {
      return;
    }

    async function fetchToken() {
      if (!partner?.id || !dashboardId || !supersetUrl) {
        return;
      }
      try {
        const newTokenInfo = await getGuestToken({
          partnerId: partner.id,
          dashboardId,
          workspaceId,
        });
        setTokenInfo(newTokenInfo);
        setupTokenRefresh(newTokenInfo.expiresAt);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to get authentication token');
      }
    }

    if (hasPresetCredentials) {
      fetchToken();
    }

    // Cleanup function to clear refresh timeout
    return () => {
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }
    };
  }, [hasPresetCredentials, partner?.id, partner?.name, dashboardId, getGuestToken]);

  useEffect(() => {
    if (!tokenInfo?.token || !dashboardId || !supersetUrl) return;

    const container = document.getElementById('my-superset-container');
    if (!container) return;

    embedDashboard({
      id: dashboardId,
      supersetDomain: supersetUrl,
      mountPoint: container,
      fetchGuestToken: () => Promise.resolve(tokenInfo.token),
      dashboardUiConfig: {
        filters: {
          expanded: false,
        },
      },
      referrerPolicy: 'strict-origin-when-cross-origin',
      iframeTitle: 'Preset Embedded Dashboard',
    });

    // Ensure the embedded iframe fills the container
    const iframe = container.querySelector('iframe');
    if (iframe) {
      iframe.style.width = '100%';
      iframe.style.height = '100%';
      iframe.style.border = 'none';
    }
  }, [tokenInfo?.token, dashboardId, supersetUrl]);

  useEffect(() => {
    const filteredResources = (data?.partners?.partners?.[0]?.analyticsResources || []).filter(
      (resource) => resource.dashboardId,
    );
    if (isStandardReportDisabled && filteredResources.length) {
      setActiveDashboard({
        dashboardId: filteredResources[0].dashboardId,
        workspaceId: filteredResources[0].workspaceId,
        supersetUrl: filteredResources[0].url,
      });
    }
  }, [isStandardReportDisabled, data]);

  if (!hasPresetCredentials) {
    return (
      <div className="p-4">
        <p className="text-gray-600">
          Analytics dashboard is not configured. Please contact your administrator.
        </p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4">
        <h2 className="text-lg font-semibold mb-2">Error Loading Dashboard</h2>
        <p className="text-red-600">{error}</p>
      </div>
    );
  }

  const shouldShowMenu = isStandardReportDisabled ? resources.length > 1 : !!resources.length;

  return (
    <div className="overflow-hidden">
      {loadingResources ? (
        <Skeleton loading className="h-10" />
      ) : shouldShowMenu ? (
        <TabNav.Root>
          {isStandardReportDisabled ? null : (
            <TabNav.Link
              onClick={() =>
                setActiveDashboard({
                  dashboardId: standardDashboardId,
                  supersetUrl: standardSupersetUrl,
                })
              }
              active={dashboardId === standardDashboardId}
            >
              Standard
            </TabNav.Link>
          )}
          {resources.map((resource) => (
            <TabNav.Link
              key={resource.id}
              onClick={() =>
                setActiveDashboard({
                  dashboardId: resource.dashboardId,
                  workspaceId: resource?.workspaceId,
                  supersetUrl: resource?.url,
                })
              }
              active={dashboardId === resource.dashboardId}
            >
              {resource.name}
            </TabNav.Link>
          ))}
        </TabNav.Root>
      ) : null}
      <div className="h-screen w-full">
        {loading || !tokenInfo?.token ? (
          <Skeleton loading className="h-full" />
        ) : (
          <div
            id="my-superset-container"
            style={{
              width: '100%',
              height: 'calc(100vh - 60px)',
              minHeight: '800px',
              minWidth: '100%',
              border: '1px solid #ccc',
              overflow: 'auto',
            }}
          />
        )}
      </div>
    </div>
  );
}
