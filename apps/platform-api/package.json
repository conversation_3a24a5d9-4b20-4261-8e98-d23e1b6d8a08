{"name": "@bybeam/platform-api", "version": "0.0.1", "private": true, "engines": {"node": "20.10.0", "npm": ">=8.19.2"}, "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc --build --force tsconfig.src.json && pnpm copy", "clean": "rm -rf dist", "copy": "copyfiles -u 1 src/**/*.graphql src/**/*.pdf  src/**/*.otf dist/", "debug": "tsc-watch --build tsconfig.src.json --onSuccess 'node --inspect -r ts-node dist/index.js'", "start": "node ./dist/index.js", "start:dev": "tsc-watch --build tsconfig.src.json --onSuccess 'node ./dist/index.js'", "test": "vitest"}, "dependencies": {"@apollo/server": "^4.11.0", "@bybeam/config-client": "workspace:^", "@bybeam/doctopus-entities": "workspace:*", "@bybeam/doctopus-types": "workspace:*", "@bybeam/entity-resolution-client": "workspace:*", "@bybeam/formatting": "workspace:^", "@bybeam/identity-client": "workspace:^", "@bybeam/infrastructure-lib": "workspace:*", "@bybeam/linking-client": "workspace:^", "@bybeam/notification-client": "workspace:^", "@bybeam/platform-entities": "workspace:*", "@bybeam/platform-lib": "workspace:*", "@bybeam/platform-types": "workspace:*", "@bybeam/scoring": "workspace:*", "@bybeam/verification-client": "workspace:*", "@bybeam/verification-types": "workspace:*", "@elastic/elasticsearch": "^8.17.1", "@google-cloud/pubsub": "4.3.3", "@google-cloud/recaptcha-enterprise": "5.1.0", "@google-cloud/storage": "7.12.1", "@graphql-tools/load-files": "7.0.0", "@graphql-tools/merge": "9.0.1", "@graphql-tools/resolvers-composition": "7.0.5", "@graphql-tools/schema": "10.0.6", "@graphql-tools/utils": "10.5.5", "@grpc/grpc-js": "1.10.10", "@pdf-lib/fontkit": "1.1.1", "axios": "1.8.2", "bcrypt": "5.1.1", "cors": "2.8.5", "csv-parse": "5.5.3", "dataloader": "2.2.2", "dayjs": "1.11.11", "dotenv": "16.4.5", "express": "4.21.0", "express-winston": "4.2.0", "flat": "5.0.2", "graphql": "16.9.0", "graphql-constraint-directive": "5.4.3", "graphql-depth-limit": "1.1.0", "graphql-scalars": "1.23.0", "graphql-upload": "16.0.2", "heic-convert": "^2.1.0", "http-status-codes": "2.3.0", "isomorphic-dompurify": "^2.10.0", "jsonwebtoken": "9.0.2", "mnemonist": "0.39.6", "multer": "2.0.1", "pdf-lib": "1.17.1", "pg": "8.13.1", "reflect-metadata": "0.1.14", "sanitize-filename": "^1.6.3", "source-map-support": "0.5.21", "temp": "0.9.4", "typeorm": "0.3.17", "uuid": "9.0.1"}, "devDependencies": {"@bybeam/common-proto": "workspace:^", "@tsconfig/node20": "20.1.4", "@types/bcrypt": "5.0.2", "@types/cors": "2.8.17", "@types/express": "4.17.21", "@types/flat": "5.0.5", "@types/graphql-depth-limit": "1.1.6", "@types/graphql-upload": "16.0.5", "@types/heic-convert": "^1.2.3", "@types/jsonwebtoken": "9.0.6", "@types/multer": "^1.4.12", "@types/node": "22.0.0", "@types/temp": "0.9.4", "@types/uuid": "9.0.8", "@vitest/coverage-v8": "2.1.9", "copyfiles": "2.4.1", "mockdate": "3.0.5", "ts-node": "10.9.2", "tsc-watch": "6.0.4", "typescript": "5.5.4", "vite-tsconfig-paths": "5.0.1", "vitest": "2.1.9"}}