import * as Doctopus from '@bybeam/doctopus-entities';
import { MeterProvider } from '@bybeam/infrastructure-lib/instrumentation';
import * as Core from '@bybeam/platform-entities';
import Repositories, {
  CoreRepositories,
  DoctopusRepositories,
  EligibilityQuestionRepository,
  ExternalRepositories,
} from '@platform-api/@types/repositories/index.js';
import { getDataSource } from '../utils/database/datasource.js';
import { ConnectionName } from '../utils/database/types.js';
import * as AddressRepository from './core/AddressRepository.js';
import * as AdminRepository from './core/AdminRepository.js';
import * as ApplicationRepository from './core/ApplicationRepository.js';
import * as ApplicationVersionRepository from './core/ApplicationVersionRepository.js';
import CaseRepository from './core/Case/CaseRepository.js';
import * as DocumentRepository from './core/DocumentRepository.js';
import * as FundRepository from './core/FundRepository.js';
import * as IncidentMessageRepository from './core/IncidentMessageRepository.js';
import * as IncomeLimitAreaRepository from './core/IncomeLimitAreaRepository.js';
import * as NoteRepository from './core/NoteRepository.js';
import * as NotificationTemplateRepository from './core/NotificationTemplateRepository.js';
import * as PaymentRepository from './core/PaymentRepository.js';
import * as ProgramDocumentRepository from './core/ProgramDocumentRepository.js';
import * as ProgramReferralRepository from './core/ProgramReferralRepository.js';
import * as ProgramRepository from './core/ProgramRepository.js';
import * as UserRepository from './core/UserRepository.js';
import * as VendorRepository from './core/VendorRepository.js';
import * as VendorTypeRepository from './core/VendorTypeRepository.js';
import * as WorkflowEventRepository from './core/WorkflowEventRepository.js';
import * as DocumentFieldRepository from './doctopus/DocumentFieldRepository.js';
import * as DocumentSummaryRepository from './doctopus/DocumentSummaryRepository.js';
import * as DocumentTagRepository from './doctopus/DocumentTagRepository.js';
import ClaimRepository from './external/ClaimRepository/index.js';
import ConfigurationRepository from './external/ConfigurationRepository.js';
import { createElasticsearchRepository } from './external/Elasticsearch/factory/index.js';
import EntityResolutionRepository from './external/EntityResolutionRespository.js';
import { GoogleCloudUploadRepository } from './external/GoogleCloudUploadRepository.js';
import IdentityRepository from './external/IdentityRepository.js';
import LinkingRepository from './external/LinkingRepository.js';
import NotificationRepository from './external/NotificationRepository.js';
import PubsubRepository from './external/PubsubRepository.js';
import VerificationRepository from './external/VerificationRepository.js';

function buildCoreRepositories(meterProvider?: MeterProvider): CoreRepositories {
  const datasource = getDataSource(ConnectionName.Default);
  return {
    Address: datasource.getRepository(Core.AddressEntity).extend(AddressRepository),
    Admin: datasource.getRepository(Core.AdminEntity).extend(AdminRepository),
    AnalyticsResource: datasource.getRepository(Core.AnalyticsResourceEntity),
    ApplicantProfile: datasource.getRepository(Core.ApplicantProfileEntity),
    ApplicantType: datasource.getRepository(Core.ApplicantTypeEntity),
    Application: datasource.getRepository(Core.ApplicationEntity).extend(ApplicationRepository),
    ApplicationAnswer: datasource.getRepository(Core.ApplicationAnswerEntity),
    ApplicationAnswerNote: datasource.getRepository(Core.ApplicationAnswerNoteEntity),
    ApplicationAnswerReview: datasource.getRepository(Core.ApplicationAnswerReviewEntity),
    ApplicationVerification: datasource.getRepository(Core.ApplicationVerificationEntity),
    ApplicationVersion: datasource
      .getRepository(Core.ApplicationVersionEntity)
      .extend(ApplicationVersionRepository),
    ApplicationScore: datasource.getRepository(Core.ApplicationScoreEntity),
    Assignment: datasource.getRepository(Core.AssignmentEntity),
    BulkOperation: datasource.getRepository(Core.BulkOperationEntity),
    Case: datasource.getRepository(Core.CaseEntity).extend(CaseRepository(meterProvider)),
    CaseMetadata: datasource.getRepository(Core.CaseMetadataViewEntity),
    CaseParticipant: datasource.getRepository(Core.CaseParticipantEntity),
    CaseTag: datasource.getRepository(Core.CaseTagEntity),
    Changelog: datasource.getRepository(Core.ChangelogEntity),
    Comment: datasource.getRepository(Core.CommentEntity),
    Document: datasource.getRepository(Core.DocumentEntity).extend(DocumentRepository),
    EligibilityConfig: datasource.getRepository(Core.EligibilityConfigEntity),
    EligibilityQuestion: datasource.getRepository(
      Core.EligibilityQuestionEntity,
    ) as EligibilityQuestionRepository,
    EnrollmentOutcome: datasource.getRepository(Core.EnrollmentOutcomeEntity),
    Enrollment: datasource.getRepository(Core.EnrollmentEntity),
    EnrollmentService: datasource.getRepository(Core.EnrollmentServiceEntity),
    Feature: datasource.getRepository(Core.FeatureEntity),
    FulfillmentMeta: datasource.getRepository(Core.FulfillmentMetaEntity),
    Fulfillment: datasource.getRepository(Core.FulfillmentEntity),
    Fund: datasource.getRepository(Core.FundEntity).extend(FundRepository),
    IncidentMessage: datasource
      .getRepository(Core.IncidentMessageEntity)
      .extend(IncidentMessageRepository),
    IncomeLimitArea: datasource
      .getRepository(Core.IncomeLimitAreaEntity)
      .extend(IncomeLimitAreaRepository),
    InvitationCode: datasource.getRepository(Core.InvitationCodeEntity),
    LatestApplicationAnswers: datasource.getRepository(Core.LatestApplicationAnswersEntity),
    LinkAttempt: datasource.getRepository(Core.LinkAttemptEntity),
    Lock: datasource.getRepository(Core.LockEntity),
    Note: datasource.getRepository(Core.NoteEntity).extend(NoteRepository),
    NotificationTemplate: datasource
      .getRepository(Core.NotificationTemplateEntity)
      .extend(NotificationTemplateRepository),
    CoreNotification: datasource.getRepository(Core.NotificationEntity),
    Outcome: datasource.getRepository(Core.OutcomeEntity),
    Partner: datasource.getRepository(Core.PartnerEntity),
    PartnerIncident: datasource.getRepository(Core.PartnerIncidentEntity),
    PartnerFeature: datasource.getRepository(Core.PartnerFeatureEntity),
    PartnerWhitelabeling: datasource.getRepository(Core.PartnerWhitelabelingEntity),
    Payment: datasource.getRepository(Core.PaymentEntity).extend(PaymentRepository),
    PaymentPattern: datasource.getRepository(Core.PaymentPatternEntity),
    ProfileAnswer: datasource.getRepository(Core.ProfileAnswerEntity),
    Program: datasource.getRepository(Core.ProgramEntity).extend(ProgramRepository),
    ProgramApplicantType: datasource.getRepository(Core.ProgramApplicantTypeEntity),
    ProgramDocument: datasource
      .getRepository(Core.ProgramDocumentEntity)
      .extend(ProgramDocumentRepository),
    ProgramFeature: datasource.getRepository(Core.ProgramFeatureEntity),
    ProgramReferral: datasource
      .getRepository(Core.ProgramReferralEntity)
      .extend(ProgramReferralRepository),
    ProgramFunds: datasource.getRepository(Core.ProgramFundEntity),
    SavedView: datasource.getRepository(Core.SavedViewEntity),
    Service: datasource.getRepository(Core.ServiceEntity),
    Tag: datasource.getRepository(Core.TagEntity),
    TaxForm: datasource.getRepository(Core.TaxFormEntity),
    TransactionAuditLog: datasource.getRepository(Core.TransactionAuditLogEntity),
    User: datasource.getRepository(Core.UserEntity).extend(UserRepository),
    Vendor: datasource.getRepository(Core.VendorEntity).extend(VendorRepository),
    VendorType: datasource.getRepository(Core.VendorTypeEntity).extend(VendorTypeRepository),
    VerificationConfig: datasource.getRepository(Core.VerificationConfigEntity),
    WorkflowEvent: datasource
      .getRepository(Core.WorkflowEventEntity)
      .extend(WorkflowEventRepository),
  };
}

function buildDoctopusRepositories(): DoctopusRepositories {
  const datasource = getDataSource(ConnectionName.Doctopus);
  return {
    Category: datasource.getRepository(Doctopus.CategoryEntity),
    DocumentSummary: datasource
      .getRepository(Doctopus.SummaryEntity)
      .extend(DocumentSummaryRepository),
    DocumentTag: datasource.getRepository(Doctopus.DocumentTagEntity).extend(DocumentTagRepository),
    DocumentField: datasource
      .getRepository(Doctopus.DocumentFieldEntity)
      .extend(DocumentFieldRepository),
    PredictionFeedback: datasource.getRepository(Doctopus.FeedbackEntity),
    Label: datasource.getRepository(Doctopus.ActiveLabelEntity),
    ModelVersion: datasource.getRepository(Doctopus.ModelVersionEntity),
    OCRResult: datasource.getRepository(Doctopus.OCRResultEntity),
    Prediction: datasource.getRepository(Doctopus.PredictionEntity),
  };
}

// Singletons
const Config = new ConfigurationRepository();
const Elasticsearch = createElasticsearchRepository();
const EntityResolution = new EntityResolutionRepository();
const Identity = new IdentityRepository();
const Linking = new LinkingRepository();
const Pubsub = new PubsubRepository();
const Notification = new NotificationRepository();

function buildExternalRepositories(): ExternalRepositories {
  return {
    Claim: new ClaimRepository(),
    Config,
    Elasticsearch,
    EntityResolution,
    Identity,
    Linking,
    Pubsub,
    Notification,
    Upload: new GoogleCloudUploadRepository(),
    Verification: new VerificationRepository(),
  };
}

export default function buildRepositories(meterProvider?: MeterProvider): Repositories {
  const coreRepositories = buildCoreRepositories(meterProvider);
  const doctopusRepositories = buildDoctopusRepositories();
  const externalRepositories = buildExternalRepositories();

  return { ...coreRepositories, ...doctopusRepositories, ...externalRepositories };
}
