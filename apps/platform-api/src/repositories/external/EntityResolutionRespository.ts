import { QueryRequest, QueryResponse, getClient } from '@bybeam/entity-resolution-client';
import { local } from '../../utils/environment.js';

export default class EntityResolutionRepository {
  private url: string;

  constructor() {
    if (!process.env.ENTITY_RESOLUTION_URL) throw new Error('ENTITY_RESOLUTION_URL not defined');
    this.url = process.env.ENTITY_RESOLUTION_URL;
  }

  private getClient() {
    return getClient(this.url, local() ? 'insecure' : 'ssl');
  }

  public query(input: QueryRequest): Promise<QueryResponse> {
    const client = this.getClient();
    return new Promise((resolve, reject) => {
      client.query(input, (error?: Error | null, response?: QueryResponse) => {
        if (error) reject(error);
        else resolve(response as QueryResponse);
        client.close();
      });
    });
  }
}
