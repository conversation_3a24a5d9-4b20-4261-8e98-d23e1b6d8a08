import { UserService, PartnerService } from '@platform-api/@types/services.js';
import dayjs from '../../utils/dayJsConfig.js';
import { getFirstName, getLastName } from '@bybeam/formatting';
import { logger } from '../../utils/logger.js';

export interface PresetGuestTokenInput {
  dashboardId: string;
  workspaceId?: string;
  userId: string;
}

export interface PresetGuestTokenResponse {
  token: string;
  expiresAt: string;
}

export interface IPresetService {
  generateGuestToken(input: PresetGuestTokenInput): Promise<PresetGuestTokenResponse>;
  refreshGuestToken(input: PresetGuestTokenInput): Promise<PresetGuestTokenResponse>;
}

interface PresetAuthResponse {
  payload: {
    access_token: string;
  };
}

interface PresetGuestTokenApiResponse {
  payload: {
    token: string;
  };
}

interface JwtPayload {
  exp: number;
  iat: number;
  sub: string;
  username?: string;
  first_name?: string;
  last_name?: string;
  resources?: Array<{
    type: string;
    id: string;
  }>;
  rls?: Array<{
    clause: string;
  }>;
}

class PresetService implements IPresetService {
  private readonly presetBaseUrl = 'https://api.app.preset.io';
  private readonly apiToken: string;
  private readonly apiSecret: string;
  private readonly team: string;
  private readonly workspaceId: string;
  private readonly userService: UserService;
  private readonly partnerService: PartnerService;
  private jwtToken: string | null = null;
  private jwtTokenExpiry: dayjs.Dayjs | null = null;

  constructor({
    userService,
    partnerService,
  }: {
    userService: UserService;
    partnerService: PartnerService;
  }) {
    const apiToken = process.env.PRESET_API_TOKEN;
    const apiSecret = process.env.PRESET_API_SECRET;
    const team = process.env.PRESET_TEAM;
    const workspaceId = process.env.PRESET_WORKSPACE_ID;

    if (!apiToken) {
      throw new Error('PRESET_API_TOKEN environment variable is required');
    }
    if (!apiSecret) {
      throw new Error('PRESET_API_SECRET environment variable is required');
    }
    if (!team) {
      throw new Error('PRESET_TEAM environment variable is required');
    }
    if (!workspaceId) {
      throw new Error('PRESET_WORKSPACE_ID environment variable is required');
    }

    this.apiToken = apiToken;
    this.apiSecret = apiSecret;
    this.team = team;
    this.workspaceId = workspaceId;
    this.userService = userService;
    this.partnerService = partnerService;
  }

  /**
   * Escapes single quotes for safe SQL-string interpolation by doubling them up.
   * Example: O'Connor → O''Connor
   */
  private autoEscape(name: string): string {
    return name.replace(/'/g, "''");
  }

  private async buildRlsClause(
    partner: { id?: string; parentId?: string; name: string } | null | undefined,
  ): Promise<{ clause: string }[]> {
    if (!partner?.id) {
      return [];
    }

    if (partner.parentId) {
      const parentPartner = await this.partnerService.findById(partner.parentId);

      // Defensive check to ensure the parent partner exists and has a name
      if (!parentPartner?.name) {
        throw new Error(`Parent partner '${partner.parentId}' not found or missing name`);
      }

      return [
        {
          clause: `parent_partner_name = '${this.autoEscape(parentPartner.name)}'`,
        },
      ];
    }

    return [
      {
        clause: `child_name = '${this.autoEscape(partner.name)}'`,
      },
    ];
  }

  private parseJwt(token: string): JwtPayload {
    try {
      const parts = token.split('.');
      if (parts.length < 2) {
        throw new Error('Token is missing payload segment');
      }

      // Convert from Base64Url to Base64
      let base64 = parts[1].replace(/-/g, '+').replace(/_/g, '/');

      // Add padding if necessary
      const padding = base64.length % 4;
      if (padding) {
        base64 += '='.repeat(4 - padding);
      }

      const jsonPayload = Buffer.from(base64, 'base64').toString('utf8');
      return JSON.parse(jsonPayload) as JwtPayload;
    } catch (error) {
      logger.error('Failed to parse JWT:', error);
      throw new Error('Invalid token format');
    }
  }

  async generateGuestToken(input: PresetGuestTokenInput): Promise<PresetGuestTokenResponse> {
    try {
      const jwtToken = await this.getJwtToken();
      const guestToken = await this.getGuestToken(jwtToken, input);

      return guestToken;
    } catch (error) {
      logger.error('Failed to generate Preset guest token:', error);
      throw new Error(
        `Failed to generate Preset guest token: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  async refreshGuestToken(input: PresetGuestTokenInput): Promise<PresetGuestTokenResponse> {
    try {
      // Check if JWT token needs refresh
      if (!this.jwtToken || !this.jwtTokenExpiry || this.jwtTokenExpiry.isBefore(dayjs())) {
        this.jwtToken = await this.getJwtToken();
      }

      const guestToken = await this.getGuestToken(this.jwtToken, input);
      return guestToken;
    } catch (error) {
      logger.error('Failed to refresh Preset guest token:', error);
      throw new Error(
        `Failed to refresh Preset guest token: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  private async getJwtToken(): Promise<string> {
    const authUrl = `${this.presetBaseUrl}/v1/auth/`;

    const response = await fetch(authUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
      body: JSON.stringify({
        name: this.apiToken,
        secret: this.apiSecret,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to authenticate with Preset: ${errorText}`);
    }

    const authData = (await response.json()) as PresetAuthResponse;
    this.jwtToken = authData.payload.access_token;
    // Set JWT token expiry to 1 hour from now (typical JWT expiry)
    this.jwtTokenExpiry = dayjs().add(1, 'hour');
    return this.jwtToken;
  }

  private async getGuestToken(
    jwtToken: string,
    input: PresetGuestTokenInput,
  ): Promise<PresetGuestTokenResponse> {
    const user = await this.userService.findWithOptions({
      relations: ['partner'],
      where: { id: input.userId },
    });

    if (!user) throw new Error('no user found');
    // Ensure the workspace identifier is safely encoded for use in the URL.
    const workspacePath = encodeURIComponent(input.workspaceId ?? this.workspaceId);
    const guestTokenUrl = `${this.presetBaseUrl}/v1/teams/${this.team}/workspaces/${workspacePath}/guest-token/`;
    const response = await fetch(guestTokenUrl, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${jwtToken}`,
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        user: {
          username: `user_${user.id}`,
          first_name: getFirstName(user.name),
          last_name: getLastName(user.name),
        },
        resources: [
          // Add custom dashboards based on analytics_resources
          {
            type: 'dashboard',
            id: input.dashboardId,
          },
        ],
        rls: await this.buildRlsClause(user.partner),
      }),
    });

    if (!response?.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to get guest token from Preset: ${errorText}`);
    }

    const guestTokenData = (await response.json()) as PresetGuestTokenApiResponse;

    try {
      // Parse the JWT token to get expiration time
      const tokenPayload = this.parseJwt(guestTokenData.payload.token);

      // Convert Unix timestamp to Date
      const expiresAt = dayjs.unix(tokenPayload.exp);

      return {
        token: guestTokenData.payload.token,
        expiresAt: expiresAt.toISOString(),
      };
    } catch (error) {
      logger.error('Token parsing error:', error);
      // Fallback to 1 hour if we can't parse the token
      const fallbackExpiry = dayjs().add(1, 'hour');
      return {
        token: guestTokenData.payload.token,
        expiresAt: fallbackExpiry.toISOString(),
      };
    }
  }
}

export default PresetService;
