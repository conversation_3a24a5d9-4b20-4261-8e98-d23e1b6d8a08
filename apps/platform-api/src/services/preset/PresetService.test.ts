import { describe, expect, it, vi, beforeEach, Mock } from 'vitest';
import PresetService, { PresetGuestTokenInput } from './PresetService.js';
import dayjs from '../../utils/dayJsConfig.js';
import { PartnerService, UserService } from '@platform-api/@types/services.js';

describe('PresetService', () => {
  let presetService: PresetService;
  let mockUserService: { findWithOptions: Mock };
  let mockFetch: Mock;
  let mockPartnerService: { findById: Mock };

  beforeEach(() => {
    process.env.PRESET_API_TOKEN = 'test-token';
    process.env.PRESET_API_SECRET = 'test-secret';
    process.env.PRESET_TEAM = 'test-team';
    process.env.PRESET_WORKSPACE_ID = 'test-workspace';

    mockUserService = {
      findWithOptions: vi.fn(),
    };

    mockPartnerService = {
      findById: vi.fn(),
    };

    mockFetch = vi.fn();
    global.fetch = mockFetch;

    presetService = new PresetService({
      userService: mockUserService as unknown as UserService,
      partnerService: mockPartnerService as unknown as PartnerService,
    });
  });

  describe('generateGuestToken', () => {
    it('should generate a guest token successfully', async () => {
      mockUserService.findWithOptions.mockResolvedValueOnce({
        id: 'user-123',
        name: 'Johnny Bravo',
        partner: { id: 'partner-123', name: 'test partner' },
      });

      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ payload: { access_token: 'mock-jwt-token' } }),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ payload: { token: 'mock-guest-token' } }),
        });

      const input: PresetGuestTokenInput = {
        dashboardId: 'dashboard-123',
        userId: 'user-123',
      };

      const { token } = await presetService.generateGuestToken(input);

      expect(mockFetch).toHaveBeenCalledTimes(2);
      expect(token).toEqual('mock-guest-token');
    });

    it('should throw an error when user is not found', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ payload: { access_token: 'mock-jwt-token' } }),
      });
      mockUserService.findWithOptions.mockResolvedValueOnce(null);

      const input: PresetGuestTokenInput = {
        dashboardId: 'dashboard-123',
        userId: 'user-123',
      };

      await expect(presetService.generateGuestToken(input)).rejects.toThrow(
        'Failed to generate Preset guest token: no user found',
      );
    });

    it('should include the workspaceId when provided', async () => {
      mockUserService.findWithOptions.mockResolvedValueOnce({
        id: 'user-123',
        name: 'Doc Holliday',
        partner: { id: 'partner-123', parentId: 'parent-123', name: 'test partner' },
      });

      mockPartnerService.findById.mockResolvedValueOnce({
        id: 'parent-123',
        name: 'Sesame Street Partner',
      });

      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ payload: { access_token: 'mock-jwt-token' } }),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ payload: { token: 'mock-guest-token' } }),
        });

      const input: PresetGuestTokenInput = {
        dashboardId: 'dashboard-123',
        userId: 'user-123',
        workspaceId: 'workspace-123',
      };

      await presetService.generateGuestToken(input);

      const tokenCall = JSON.parse(mockFetch.mock.calls[1][1].body);
      expect(tokenCall.rls).toContainEqual({
        clause: "parent_partner_name = 'Sesame Street Partner'",
      });
      expect(mockFetch.mock.calls[1][0]).toContain('workspace-123');
    });
  });

  describe('refreshGuestToken', () => {
    it('should refresh a guest token when JWT is expired', async () => {
      // biome-ignore lint/suspicious/noExplicitAny: setting private properties for testing
      (presetService as any).jwtToken = 'expired-token';
      // biome-ignore lint/suspicious/noExplicitAny: setting private properties for testing
      (presetService as any).jwtTokenExpiry = dayjs().subtract(1, 'hour');

      mockUserService.findWithOptions.mockResolvedValueOnce({
        id: 'user-123',
        name: 'Clark Gable',
        partner: { id: 'partner-123', name: 'test partner' },
      });

      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ payload: { access_token: 'new-jwt-token' } }),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ payload: { token: 'new-guest-token' } }),
        });

      const input: PresetGuestTokenInput = {
        dashboardId: 'dashboard-123',
        userId: 'user-123',
      };

      const result = await presetService.refreshGuestToken(input);

      expect(mockFetch).toHaveBeenCalledTimes(2);
      expect(result).toHaveProperty('token', 'new-guest-token');
    });

    it('should reuse existing JWT token if not expired', async () => {
      // biome-ignore lint/suspicious/noExplicitAny: setting private properties for testing
      (presetService as any).jwtToken = 'valid-token';
      // biome-ignore lint/suspicious/noExplicitAny: setting private properties for testing
      (presetService as any).jwtTokenExpiry = dayjs().add(30, 'minutes');

      mockUserService.findWithOptions.mockResolvedValueOnce({
        id: 'user-123',
        name: 'John Doe',
        partner: { id: 'partner-123', name: 'test partner' },
      });

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ payload: { token: 'refreshed-guest-token' } }),
      });

      const input: PresetGuestTokenInput = {
        dashboardId: 'dashboard-123',
        userId: 'user-123',
      };

      const result = await presetService.refreshGuestToken(input);

      expect(mockFetch).toHaveBeenCalledTimes(1);
      expect(result).toHaveProperty('token', 'refreshed-guest-token');
    });
  });

  describe('parseJwt', () => {
    it('should correctly parse a JWT token', () => {
      const payload = { exp: 1234567890, sub: 'test-user', iat: 90 };
      const base64Payload = Buffer.from(JSON.stringify(payload))
        .toString('base64')
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=+$/, '');
      const mockToken = `header.${base64Payload}.signature`;
      // biome-ignore lint/suspicious/noExplicitAny: setting private properties for testing
      const result = (presetService as any).parseJwt(mockToken);

      expect(result).toEqual(payload);
    });
  });

  describe('buildRlsClause', () => {
    it("should escape single quotes in partner's name for child_name clause", async () => {
      const partner = { id: 'p1', name: "O'Reilly", parentId: undefined };

      // biome-ignore lint/suspicious/noExplicitAny: accessing private method for testing
      const result = await (presetService as any).buildRlsClause(partner);

      expect(result).toEqual([{ clause: "child_name = 'O''Reilly'" }]);
    });

    it('should throw when parent partner not found or missing name', async () => {
      const partner = { id: 'c1', parentId: 'parent-123', name: 'Child Corp' };
      mockPartnerService.findById.mockResolvedValueOnce(null);

      // biome-ignore lint/suspicious/noExplicitAny: accessing private method for testing
      await expect((presetService as any).buildRlsClause(partner)).rejects.toThrow(
        "Parent partner 'parent-123' not found or missing name",
      );
    });
  });
});
