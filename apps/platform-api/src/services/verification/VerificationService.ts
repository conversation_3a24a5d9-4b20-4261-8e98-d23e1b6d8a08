import assertExhaustive from '@bybeam/platform-lib/utilities/assertExhaustive';
import { FeatureName, Nullable, ProgramDocumentStatus, UpdateVerificationConfigInput, VerificationConfig } from '@bybeam/platform-types';
import {
  DataLookupConfiguration,
  ServiceType,
  VerificationConfiguration,
} from '@bybeam/verification-client';
import { USER_FIELDS, UpsertLookupConfigInput } from '@bybeam/verification-types';
import {
  ApplicationVerificationRepository,
  VerificationConfigRepository,
  VerificationRepository,
} from '@platform-api/@types/repositories/index.js';
import { ProgramDocumentService, ProgramFeatureService } from '@platform-api/@types/services.js';
import EntityResolutionRepository from '@platform-api/repositories/external/EntityResolutionRespository.js';
import {
  ApplicationSubmissionEvent,
  BuildVerificationPayloadInput,
  EventProducer,
  UploadVerificationFileEvent,
} from '../../@types/events.js';
import { VerificationService as IVerificationService } from '../../@types/services.js';
import { getAnswer } from '../../utils/answers.js';
import { logger } from '../../utils/logger.js';
import ManyToOneDataLoader from '../utils/ManyToOneDataLoader.js';
import { In } from 'typeorm';

interface Dependencies {
  readonly repository: VerificationConfigRepository;
  readonly applicationVerificationRepository: ApplicationVerificationRepository;
  readonly eventProducer: EventProducer;
  readonly entityResolutionRepository: EntityResolutionRepository;
  readonly programDocumentService: ProgramDocumentService;
  readonly programFeatureService: ProgramFeatureService;
  readonly verificationRepository: VerificationRepository;
}

export default class VerificationService implements IVerificationService {
  readonly repository: VerificationConfigRepository;
  readonly applicationVerificationRepository: ApplicationVerificationRepository;
  readonly entityResolutionRepository: EntityResolutionRepository;
  readonly eventProducer: EventProducer;
  readonly programDocumentService: ProgramDocumentService;
  readonly programFeatureService: ProgramFeatureService;
  readonly verificationRepository: VerificationRepository;

  constructor({
    repository,
    applicationVerificationRepository,
    entityResolutionRepository,
    eventProducer,
    programDocumentService,
    programFeatureService,
    verificationRepository,
  }: Dependencies) {
    this.repository = repository;
    this.applicationVerificationRepository = applicationVerificationRepository;
    this.entityResolutionRepository = entityResolutionRepository;
    this.eventProducer = eventProducer;
    this.programDocumentService = programDocumentService;
    this.programFeatureService = programFeatureService;
    this.verificationRepository = verificationRepository;
  }


  private readonly programConfigurationDataloader = new ManyToOneDataLoader(
    ({ programId }) => programId,
    async (programIds: string[]) =>
      (await this.verificationRepository.getConfigurations({ programIds })).configurations,
  );

  /**
   * @deprecated moving to ERS-based verification. Use findConfigByProgramId instead.
   */
  public async findConfigurationByProgramId(id: string): Promise<VerificationConfiguration[]> {
    return this.programConfigurationDataloader.load(id);
  }

  /**
   * @deprecated moving to ERS-based verification. Use verifyApplication instead.
   */
  public async verify({
    applicationVersionId,
    programId,
    answers,
    submitter,
  }: ApplicationSubmissionEvent) {
    if (!submitter.applicantProfile)
      throw new Error('Please provide applicant profile to verification request');

    const service = await this.getServiceType(programId);
    if (!service) return;

    logger.info(
      `${this.constructor.name}.verify: sending for applicationVersionId ${applicationVersionId}`,
    );

    const config = (await this.findConfigurationByProgramId(programId)).find(
      (cf) =>
        cf.service === service &&
        cf.applicantTypeId === submitter.applicantProfile?.applicantTypeId,
    );
    if (!config) return;

    const payload = await this.buildPayload({
      answers,
      service,
      submitter,
      config,
    });

    try {
      const response = await this.verificationRepository.verify({
        applicantTypeId: submitter.applicantProfile.applicantTypeId,
        programId,
        service,
        payload,
      });
      logger.info(
        `${this.constructor.name}.verify: saving response for applicationVersionId ${applicationVersionId}`,
      );
      this.applicationVerificationRepository.save({ applicationVersionId, service, ...response });
    } catch (error) {
      logger.error({ error }, 'VerificationService.verify: unexpected error returned from client');
    }
  }

  /**
   * @deprecated moving to ERS-based verification. Not needed in v2.
   */
  public async uploadFile({ programDocumentId, ...request }: UploadVerificationFileEvent) {
    const service = await this.getServiceType(request.programId);
    if (!service) return;
    logger.info(
      { request },
      `${this.constructor.name}.request: upsert config for programId: ${request.programId}, document: ${programDocumentId}`,
    );

    let status = ProgramDocumentStatus.Completed;
    try {
      await this.verificationRepository.uploadFile({ ...request, service });
      this.programConfigurationDataloader.clear(request.programId);
    } catch (error) {
      logger.error(
        { error },
        `VerificationService.uploadFile: unexpected error returned from client => program: ${request.programId}, document: ${programDocumentId}`,
      );
      status = ProgramDocumentStatus.Failed;
    } finally {
      await this.programDocumentService.save({ id: programDocumentId, status });
    }
  }

  /**
   * @deprecated moving to ERS-based verification. Not needed in v2.
   */
  public async upsertLookupConfig(request: UpsertLookupConfigInput) {
    const { id, ...rest } = request;
    const isAllowed = await this.hasDataLookupFeature(id);
    if (!isAllowed) return;
    logger.info(
      { request },
      `${this.constructor.name}.request: updating lookup config for program: ${request.id}`,
    );
    try {
      await this.verificationRepository.upsertLookupConfig({ programId: id, ...rest });
      this.programConfigurationDataloader.clear(id);
    } catch (error) {
      logger.error(
        { error },
        `VerificationService.updateLookupConfig: unexpected error returned from client => program: ${request.id}`,
      );
    }
  }

  private buildDataLookupPayload({
    answers,
    config: { fields },
    submitter,
  }: Pick<BuildVerificationPayloadInput, 'answers' | 'submitter'> & {
    config: DataLookupConfiguration;
  }): {
    key: string;
    value: string;
  }[] {
    return fields
      .filter((field) => !field?.metadata)
      .map(({ key }) => {
        let value = undefined;
        if (USER_FIELDS.includes(key))
          value = `${submitter[key as keyof BuildVerificationPayloadInput['submitter']]}`;
        else value = getAnswer<string>(key, answers);
        return { key, value };
      });
  }

  private async buildPayload({
    service,
    config,
    ...data
  }: BuildVerificationPayloadInput): Promise<{ key: string; value: string }[]> {
    switch (service) {
      case ServiceType.DataLookup:
        return this.buildDataLookupPayload({
          ...data,
          config: config.dataLookup as DataLookupConfiguration,
        });
      case ServiceType.UnknownServiceType:
        throw new Error(`${service} unsupported`);
      default:
        return assertExhaustive(service);
    }
  }

  private async hasDataLookupFeature(programId: string): Promise<boolean> {
    return await this.programFeatureService.hasFeatures({
      programId,
      features: [FeatureName.ApplicationVerification, FeatureName.VerificationDataLookup],
    });
  }

  private async getServiceType(programId: string): Promise<Nullable<ServiceType>> {
    const isAllowed = await this.hasDataLookupFeature(programId);
    if (isAllowed) return ServiceType.DataLookup;
    return;
  }

  private readonly programConfigsDataloader = new ManyToOneDataLoader(
    ({ programId }) => programId,
    async (programIds: string[]) =>
      (await this.repository.findBy({ programId: In(programIds) })),
  );


  public async findConfigByProgramId(id: string): Promise<VerificationConfig> {
    return (await this.programConfigsDataloader.load(id))[0];
  }

  public async updateConfig(input: UpdateVerificationConfigInput): Promise<void> {
    await this.repository.update(input.id, input);
    this.programConfigsDataloader.clear(input.id);
  }
  

  public async verifyApplication(input: ApplicationSubmissionEvent): Promise<void> {
    const config = await this.findConfigByProgramId(input.programId);
    if (!config.filepath) return;

    const ersResponse = await this.entityResolutionRepository.query({
      entities: [{
        entityId: input.applicationVersionId,
        fields: config.config.fields
          .filter((field) => !field?.metadata)
          .map(({ key }) => {
            let value = undefined;
            if (USER_FIELDS.includes(key))
              value = `${input.submitter[key as keyof BuildVerificationPayloadInput['submitter']]}`;
            else value = getAnswer<string>(key, input.answers);

            let fieldType = 'FIELD_TYPE_UNKNOWN';
            if (key === 'first_name') fieldType = 'GIVEN_NAME';
            if (key === 'last_name') fieldType = 'FAMILY_NAME';
            if (key === 'date_of_birth') fieldType = 'DATE_OF_BIRTH';
            return { fieldName: key, value, fieldType: 'FIELD_TYPE_UNKNOWN' };
          })
      }],
      features: [],
      datasource: {
        cloudStorage: {
          path: config.filepath,
          format: 'CSV',
          encoding: 'UTF8',
          _encoding: 'encoding'
        },
        source: 'cloudStorage',
      },
      model: 'HEURISTIC_EXAMPLE',
      maxResults: 1,
      _confidenceThreshold: 'confidenceThreshold',
      _maxResults: 'maxResults',
    });

    console.log({ ersResponse });
  }
}
