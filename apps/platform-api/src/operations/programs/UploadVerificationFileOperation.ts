import { sameMembers } from '@bybeam/platform-lib/utilities/array';
import {
  FeatureName,
  MutationResponse,
  Program,
  ProgramDocument,
  ProgramDocumentStatus,
  UploadEntityDocumentInput,
} from '@bybeam/platform-types';
import { ServiceType } from '@bybeam/verification-client';
import { FileUpload } from 'graphql-upload/Upload.mjs';
import { StatusCodes } from 'http-status-codes';
import { AdminToken } from '../../@types/authentication.js';
import { EventProducer, Events, UploadVerificationFileEvent } from '../../@types/events.js';
import { Services } from '../../@types/services.js';
import { readCSVHeaders } from '../../utils/file.js';
import { logger } from '../../utils/logger.js';
import * as response from '../../utils/response.js';
import AbstractOperation from '../AbstractOperation.js';

export default class UploadVerificationFileOperation extends AbstractOperation<
  Pick<
    Services,
    'documents' | 'programs' | 'programDocuments' | 'programFeatures' | 'verification'
  > & {
    events: EventProducer;
  },
  Pick<Program, 'id'>,
  MutationResponse<Program>,
  AdminToken
> {
  private async validateVerificationFile(program: Program, file: Promise<FileUpload>) {
    if (!program) {
      return {
        statusCode: StatusCodes.NOT_FOUND,
        errors: ['Program not found.'],
      };
    }
    if (
      !(await this.dependencies.programFeatures.hasFeatures({
        programId: program.id,
        features: [FeatureName.ApplicationVerification, FeatureName.VerificationDataLookup],
      }))
    ) {
      return {
        statusCode: StatusCodes.BAD_REQUEST,
        errors: ['Program does not have requried features.'],
      };
    }

    if (
      program.documents?.length &&
      program.documents.some(
        (doc: ProgramDocument) => doc.status === ProgramDocumentStatus.InProgress,
      )
    )
      return {
        statusCode: StatusCodes.BAD_REQUEST,
        errors: ['Program has a verification file in progress.'],
      };

    const document = await file;
    if (!document || !document?.mimetype?.includes('csv')) {
      return {
        statusCode: StatusCodes.BAD_REQUEST,
        errors: ['Verification file should have csv format.'],
      };
    }

    const config = (
      await this.dependencies.verification.findConfigurationByProgramId(program.id)
    ).find(({ service }) => service === ServiceType.DataLookup);
    if (!config || !config.dataLookup?.fields.length) {
      return {
        statusCode: StatusCodes.BAD_REQUEST,
        errors: ['Program does not have verification setup.'],
      };
    }

    const headers = await readCSVHeaders(document);
    const verificationFileKeys = config.dataLookup.fields.map(({ key }) => key);

    if (!sameMembers(headers, verificationFileKeys)) {
      return {
        statusCode: StatusCodes.BAD_REQUEST,
        errors: ['File format does not match with configuration.'],
      };
    }
  }

  public async run(
    token: AdminToken,
    { id, file }: UploadEntityDocumentInput,
  ): Promise<MutationResponse<Program>> {
    try {
      this.log('info', `uploading verification file for program ${id}`);
      let status = ProgramDocumentStatus.InProgress;
      const program = (await this.dependencies.programs.findWithOptions({
        where: { id },
        relations: ['partner', 'documents'],
      })) as Program;

      const validationError = await this.validateVerificationFile(program, file);
      if (validationError) {
        logger.warn({ validationError }, `${this.constructor.name}: validation error >`);
        return response.error(validationError);
      }

      const [verificationConfigDocument] = await this.dependencies.documents.upload(token, {
        relation: { type: 'program', id },
        files: [file],
      });

      await this.dependencies.programDocuments.save({
        programId: id,
        documentId: verificationConfigDocument.id,
        status: ProgramDocumentStatus.InProgress,
      });

      await this.dependencies.

      // const config = (
      //   await this.dependencies.verification.findConfigurationByProgramId(program.id)
      // ).find(({ service }) => service === ServiceType.DataLookup);
      // this.dependencies.events.emit<UploadVerificationFileEvent>(Events.UploadVerificationFile, {
      //   applicantTypeId: config?.applicantTypeId ?? '',
      //   programId: id,
      //   filepath: `${program.partner.externalId}/${verificationConfigDocument.documentKey}`,
      //   programDocumentId,
      // });

      return response.success({
        statusCode: StatusCodes.OK,
        record: await this.dependencies.programs.findById(program.id),
      });
    } catch (error) {
      logger.error({ error }, `${this.constructor.name}: unexpected error for program ${id} >`);
      return response.error();
    }
  }
}
