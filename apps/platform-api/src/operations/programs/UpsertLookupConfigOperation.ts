import { MutationResponse, Program } from '@bybeam/platform-types';
import { StatusCodes } from 'http-status-codes';
import { AdminToken } from '../../@types/authentication.js';
import { Services } from '../../@types/services.js';
import { logger } from '../../utils/logger.js';
import * as response from '../../utils/response.js';
import AbstractOperation from '../AbstractOperation.js';
import { UpsertLookupConfigInput } from '@bybeam/verification-types';

export default class UpsertLookupConfigOperation extends AbstractOperation<
  Pick<Services, 'verification' | 'programs'>,
  UpsertLookupConfigInput,
  MutationResponse<Program>,
  AdminToken
> {
  private async validateRequest(programId: string) {
    const program = (await this.dependencies.programs.findWithOptions({
      where: { id: programId },
    })) as Program;
    if (!program) {
      return {
        statusCode: StatusCodes.NOT_FOUND,
        errors: ['Program not found.'],
      };
    }
  }

  public async run(
    _: AdminToken,
    { id, ...rest }: UpsertLookupConfigInput,
  ): Promise<MutationResponse<Program>> {
    try {
      const validationError = await this.validateRequest(id);
      if (validationError) {
        logger.warn({ validationError }, `${this.constructor.name}: validation error >`);
        return response.error(validationError);
      }
      await this.dependencies.verification.upsertLookupConfig({ id, ...rest });
      return response.success({
        statusCode: StatusCodes.OK,
        record: await this.dependencies.programs.findById(id),
      });
    } catch (error) {
      logger.error(
        { error },
        `${this.constructor.name}: unexpected lookup config error for program ${id} >`,
      );
      return response.error();
    }
  }
}
