import {
  Address,
  AddressRelationType,
  Admin,
  AdminSortColumn,
  AggregateApplicantsReferred,
  AggregateApplication,
  AggregateApplicationFilter,
  AggregatePaymentFilter,
  AggregatePayments,
  AnalyticsResource,
  ApplicantProfile,
  ApplicantType,
  Application,
  ApplicationAnswer,
  ApplicationAnswerNote,
  ApplicationAnswerReview,
  ApplicationScore,
  ApplicationVerification,
  ApplicationVersion,
  Assignment,
  BulkOperation,
  Case,
  CaseFilter,
  CaseMetadata,
  CaseParticipant,
  CaseSortColumn,
  CaseStatusCounts,
  CaseTag,
  Changelog,
  Comment,
  CommunicationChannels,
  Document,
  DocumentRelationType,
  EligibilityConfig,
  EligibilityQuestion,
  Enrollment,
  EnrollmentOutcome,
  EnrollmentService,
  Feature,
  Fulfillment,
  FulfillmentMeta,
  Fund,
  FundFilter,
  FundPayment,
  FundSortColumn,
  IncidentMessage,
  IncomeLimitArea,
  IncomeLimitAreaFilter,
  InvitationCode,
  LinkAttempt,
  Lock,
  Note,
  NoteRelationType,
  Notification,
  NotificationTemplate,
  NotificationType,
  OffsetPagination,
  Outcome,
  Partner,
  PartnerFeature,
  PartnerIncident,
  PartnerWhitelabeling,
  Payment,
  PaymentPattern,
  PaymentSortColumn,
  PaymentStatus,
  ProfileAnswer,
  Program,
  ProgramApplicantType,
  ProgramDocument,
  ProgramFeature,
  ProgramFund,
  ProgramFunds,
  ProgramReferral,
  SavedView,
  Service,
  Sort,
  Tag,
  TaxForm,
  TransactionAuditLog,
  User,
  UserFilter,
  UserSortColumn,
  Vendor,
  VendorFilter,
  VendorSortColumn,
  VendorType,
  VerificationConfig,
  WorkflowEvent,
  WorkflowSummary,
  WorkflowSummaryFilter,
} from '@bybeam/platform-types';
import { Repository } from 'typeorm';
import { LoginToken } from '../authentication.js';

export interface AddressRepository extends Repository<Address> {
  findByRelationIds(
    ids: string[],
    relation: AddressRelationType,
  ): Promise<{ id: string; addressIds: string[] }[]>;
}

export interface AdminRepository extends Repository<Admin> {
  countAdminAssignments(partnerId: string, cap?: number): Promise<{ id: string; count: number }[]>;
  findAndCountIds(
    this: AdminRepository,
    filter: { partnerId: string },
    pagination?: OffsetPagination,
    sort?: Sort<AdminSortColumn>,
  ): Promise<{ ids: string[]; count: number }>;
}

export interface ApplicationRepository extends Repository<Application> {
  aggregate(
    filter: AggregateApplicationFilter & { partnerId: string },
  ): Promise<AggregateApplication>;
}

export interface ApplicationVersionRepository extends Repository<ApplicationVersion> {
  findLatestVersions(ids: string[]): Promise<ApplicationVersion[]>;
}

export interface CaseRepository extends Repository<Case> {
  findAndCountIds(
    filter: CaseFilter & { partnerId: string; partnerProgramIds?: string[] },
    pagination?: OffsetPagination,
    sort?: Sort<CaseSortColumn>,
    token?: LoginToken,
  ): Promise<{ ids: string[]; count: number }>;
}

export interface DocumentRepository extends Repository<Document> {
  findByRelationIds(
    ids: string[],
    relation: DocumentRelationType,
  ): Promise<{ id: string; documentIds: string[] }[]>;
}

export interface FundRepository extends Repository<Fund> {
  getStats(ids: string[], statuses: PaymentStatus[]): Promise<FundPayment[]>;
  findAndCountRows(
    filter: FundFilter & { partnerId: string },
    pagination?: OffsetPagination,
    sort?: Sort<FundSortColumn>,
  ): Promise<[rows: Fund[], count: number]>;
}

export interface IncidentMessageRepository extends Repository<IncidentMessage> {
  findByPartnerId(partnerId?: string): Promise<[IncidentMessage[], number]>;
}

export interface IncomeLimitAreaRepository extends Repository<IncomeLimitArea> {
  findBySearchFilter({ search, state, county }: IncomeLimitAreaFilter): Promise<{
    incomeLimitAreas: IncomeLimitArea[];
    count: number;
  }>;
}

export interface NoteRepository extends Repository<Note> {
  addToRelation(input: {
    relationType: NoteRelationType;
    notes: { id: string; relationId: string }[];
  }): Promise<void>;
  findByRelationIds(
    ids: string[],
    relation: NoteRelationType,
  ): Promise<{ id: string; noteIds: string[] }[]>;
}

export interface PaymentRepository extends Repository<Payment> {
  findByVendorId(
    id: string,
    pagination?: OffsetPagination,
    sort?: Sort<PaymentSortColumn>,
  ): Promise<{ ids: string[]; count: number }>;

  aggregate(filter: AggregatePaymentFilter & { partnerId?: string }): Promise<AggregatePayments>;
  aggregate(
    filter: AggregatePaymentFilter & { partnerId?: string },
    aggregator: 'applicant' | 'payee' | 'program',
  ): Promise<(AggregatePayments & { id: string })[]>;
  aggregate(
    filter: AggregatePaymentFilter & { partnerId?: string },
    aggregator?: 'applicant' | 'payee' | 'program',
  ): Promise<AggregatePayments | (AggregatePayments & { id?: string })[]>;
}

export interface ProgramDocumentRepository extends Repository<ProgramDocument> {
  findLatestDocument(ids: string[]): Promise<ProgramDocument[]>;
}

export interface ProgramReferralRepository extends Repository<ProgramReferral> {
  aggregateApplicantsReferred(partnerId: string): Promise<AggregateApplicantsReferred>;
}

export interface ProgramRepository extends Repository<Program> {
  getStatsByFund(ids: string[], paymentStatuses: PaymentStatus[]): Promise<ProgramFunds[]>;
  getCaseCounts(ids: string[]): Promise<CaseStatusCounts[]>;
}

export interface UserRepository extends Repository<User> {
  findAndCountIds(
    filter: UserFilter & { partnerId: string },
    pagination?: OffsetPagination,
    sort?: Sort<UserSortColumn>,
  ): Promise<{ ids: string[]; count: number }>;
}

export interface VendorRepository extends Repository<Vendor> {
  findAndCountIds(
    filter: VendorFilter & { partnerId: string },
    pagination?: OffsetPagination,
    sort?: Sort<VendorSortColumn>,
  ): Promise<{ ids: string[]; count: number }>;
}

export interface VendorTypeRepository extends Repository<VendorType> {
  findByVendorIds(ids: string[]): Promise<{ id: string; vendorTypeIds: string[] }[]>;
}

export interface WorkflowEventRepository extends Repository<WorkflowEvent> {
  summarize({ partnerId, programId, createdAt }: WorkflowSummaryFilter): Promise<WorkflowSummary>;
}

export interface NotificationTemplateRepository extends Repository<NotificationTemplate> {
  findByProgramId(input: {
    partnerId: string;
    programId: string;
    types?: NotificationType[];
    channels?: CommunicationChannels[];
  }): Promise<NotificationTemplate[]>;
}

// Unextended repositories
export type AnalyticsRepository = Repository<AnalyticsResource>;
export type ApplicantProfileRepository = Repository<ApplicantProfile>;
export type ApplicantTypeRepository = Repository<ApplicantType>;
export type ApplicationAnswerRepository = Repository<ApplicationAnswer>;
export type ApplicationAnswerNoteRepository = Repository<ApplicationAnswerNote>;
export type ApplicationAnswerReviewRepository = Repository<ApplicationAnswerReview>;
export type ApplicationScoreRepository = Repository<ApplicationScore>;
export type ApplicationVerificationRepository = Repository<ApplicationVerification>;
export type AssignmentRepository = Repository<Assignment>;
export type BulkOperationRepository = Repository<BulkOperation>;
export type CaseMetadataRepository = Repository<CaseMetadata>;
export type CaseParticipantRepository = Repository<CaseParticipant>;
export type CaseTagRepository = Repository<CaseTag>;
export type ChangelogRepository = Repository<Changelog>;
export type CommentRepository = Repository<Comment>;
export type EligibilityConfigRepository = Repository<EligibilityConfig>;
export type EligibilityQuestionRepository = Repository<EligibilityQuestion>;
export type EnrollmentOutcomeRepository = Repository<EnrollmentOutcome>;
export type EnrollmentRepository = Repository<Enrollment>;
export type EnrollmentServiceRepository = Repository<EnrollmentService>;
export type FeatureRepository = Repository<Feature>;
export type FulfillmentMetaRepository = Repository<FulfillmentMeta>;
export type FulfillmentRepository = Repository<Fulfillment>;
export type InvitationCodeRepository = Repository<InvitationCode>;
export type LatestApplicationAnswersRepository = Repository<
  Pick<ApplicationAnswer, 'id' | 'key' | 'value'> & { applicationId: string }
>;
export type LinkAttemptRepository = Repository<LinkAttempt>;
export type LockRepository = Repository<Lock>;
export type CoreNotificationRepository = Repository<Notification>;
export type OutcomeRepository = Repository<Outcome>;
export type PartnerRepository = Repository<Partner>;
export type PartnerIncidentRepository = Repository<PartnerIncident>;
export type PartnerFeatureRepository = Repository<PartnerFeature>;
export type PartnerWhitelabelingRepository = Repository<PartnerWhitelabeling>;
export type PaymentPatternRepository = Repository<PaymentPattern>;
export type ProfileAnswerRepository = Repository<ProfileAnswer>;
export type ProgramApplicantTypeRepository = Repository<ProgramApplicantType>;
export type ProgramFeatureRepository = Repository<ProgramFeature>;
export type ProgramFundRepository = Repository<ProgramFund>;
export type SavedViewRepository = Repository<SavedView>;
export type ServiceRepository = Repository<Service>;
export type TagsRepository = Repository<Tag>;
export type TaxFormRepository = Repository<TaxForm>;
export type TransactionAuditLogRepository = Repository<TransactionAuditLog>;
export type VerificationConfigRepository = Repository<VerificationConfig>;

interface CoreRepositories {
  Address: AddressRepository;
  Admin: AdminRepository;
  AnalyticsResource: AnalyticsRepository;
  ApplicantProfile: ApplicantProfileRepository;
  ApplicantType: ApplicantTypeRepository;
  Application: ApplicationRepository;
  ApplicationAnswer: ApplicationAnswerRepository;
  ApplicationAnswerNote: ApplicationAnswerNoteRepository;
  ApplicationAnswerReview: ApplicationAnswerReviewRepository;
  ApplicationScore: ApplicationScoreRepository;
  ApplicationVerification: ApplicationVerificationRepository;
  ApplicationVersion: ApplicationVersionRepository;
  Assignment: AssignmentRepository;
  BulkOperation: BulkOperationRepository;
  Case: CaseRepository;
  CaseMetadata: CaseMetadataRepository;
  CaseParticipant: CaseParticipantRepository;
  CaseTag: CaseTagRepository;
  Changelog: ChangelogRepository;
  Comment: CommentRepository;
  Document: DocumentRepository;
  EligibilityConfig: EligibilityConfigRepository;
  EligibilityQuestion: EligibilityQuestionRepository;
  Enrollment: EnrollmentRepository;
  EnrollmentOutcome: EnrollmentOutcomeRepository;
  EnrollmentService: EnrollmentServiceRepository;
  Feature: FeatureRepository;
  Fulfillment: FulfillmentRepository;
  FulfillmentMeta: FulfillmentMetaRepository;
  Fund: FundRepository;
  IncidentMessage: IncidentMessageRepository;
  IncomeLimitArea: IncomeLimitAreaRepository;
  InvitationCode: InvitationCodeRepository;
  LatestApplicationAnswers: LatestApplicationAnswersRepository;
  LinkAttempt: LinkAttemptRepository;
  Lock: LockRepository;
  Note: NoteRepository;
  NotificationTemplate: NotificationTemplateRepository;
  CoreNotification: CoreNotificationRepository;
  Outcome: OutcomeRepository;
  Partner: PartnerRepository;
  PartnerIncident: PartnerIncidentRepository;
  PartnerFeature: PartnerFeatureRepository;
  PartnerWhitelabeling: PartnerWhitelabelingRepository;
  Payment: PaymentRepository;
  PaymentPattern: PaymentPatternRepository;
  ProfileAnswer: ProfileAnswerRepository;
  Program: ProgramRepository;
  ProgramApplicantType: ProgramApplicantTypeRepository;
  ProgramDocument: ProgramDocumentRepository;
  ProgramFeature: ProgramFeatureRepository;
  ProgramReferral: ProgramReferralRepository;
  ProgramFunds: ProgramFundRepository;
  SavedView: SavedViewRepository;
  Service: ServiceRepository;
  Tag: TagsRepository;
  TaxForm: TaxFormRepository;
  TransactionAuditLog: TransactionAuditLogRepository;
  User: UserRepository;
  Vendor: VendorRepository;
  VendorType: VendorTypeRepository;
  VerificationConfig: VerificationConfigRepository;
  WorkflowEvent: WorkflowEventRepository;
}

export type { CoreRepositories };
