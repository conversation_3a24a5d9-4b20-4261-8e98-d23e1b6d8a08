import {
  ApplicantProfileFilter,
  CreateProgramApplicationConfigurationRequest,
  GetApplicantProfileConfigurationsResponse,
  GetPartnerCommunicationChannelsConfigurationRequest,
  GetPartnerCommunicationChannelsConfigurationResponse,
  GetProgramApplicationConfigurationsRequest,
  GetProgramApplicationConfigurationsResponse,
  ProgramApplicationConfiguration,
} from '@bybeam/config-client';
import {
  AccessRequestList,
  CanAccessResponse,
  CheckPermissionRequest,
  CreateAccessRequest,
  CreateRelationshipsRequest,
  CreateUserRequest,
  CreateUserResponse,
  GetAccessRequests,
  GetAdminsResponse,
  GetCoreUsersRequest,
  GetUserRequest,
  ReadPortalRolesRequest,
  ReadPortalRolesResponse,
  RecaptchaRequest,
  RecaptchaResponse,
  Response,
  ReviewAccessRequest,
  SendMagicLinkRequest,
  SendVerificationEmailRequest,
  SessionRequest,
  SessionResponse,
  UpdateUserRequest,
  UserResponse,
  ValidateRequest,
  VerifyEmailRequest,
} from '@bybeam/identity-client/types';
import {
  InviteCaseParticipantRequest,
  InviteCaseParticipantResponse,
  UnlinkCaseParticipantRequest,
  UnlinkCaseParticipantResponse,
} from '@bybeam/linking-client';
import {
  NotifyRequest__Optional,
  NotifyResponse,
  SendEmailRequest__Optional,
  SendEmailResponse,
} from '@bybeam/notification-client';
import {
  GetConfigurationsRequest,
  GetConfigurationsResponse,
  UploadFileRequest,
  UploadFileResponse,
  UpsertLookupConfigRequest__Optional,
  UpsertLookupConfigResponse,
  VerificationRequest,
  VerificationResponse,
} from '@bybeam/verification-client';
import EntityResolutionRepository from '@platform-api/repositories/external/EntityResolutionRespository.js';
import { ElasticsearchRepository } from '../../repositories/external/Elasticsearch/ElasticsearchRepository.js';
import PubsubRepository from '../../repositories/external/PubsubRepository.js';
import { ClaimRepository } from '../claim/index.js';
import { UploadRepository } from '../upload.js';

export interface ConfigRepository {
  getProgramApplicationConfigurations(
    request: GetProgramApplicationConfigurationsRequest,
  ): Promise<GetProgramApplicationConfigurationsResponse>;
  getApplicantProfileConfigurations(
    request: ApplicantProfileFilter[],
  ): Promise<GetApplicantProfileConfigurationsResponse>;
  getPartnerCommunicationChannelsConfiguration(
    request: GetPartnerCommunicationChannelsConfigurationRequest,
  ): Promise<GetPartnerCommunicationChannelsConfigurationResponse>;
  createProgramApplicationConfiguration(
    request: CreateProgramApplicationConfigurationRequest,
  ): Promise<ProgramApplicationConfiguration>;
}

export interface IdentityRepository {
  beginSession(request: SessionRequest): Promise<SessionResponse>;
  checkPermission(request: CheckPermissionRequest): Promise<CanAccessResponse>;
  createAccessRequest(request: CreateAccessRequest): Promise<Response>;
  createRelationships(request: CreateRelationshipsRequest): Promise<Response>;
  createUser(request: CreateUserRequest): Promise<CreateUserResponse>;
  getAccessRequests(request: GetAccessRequests): Promise<AccessRequestList>;
  getAdmins(request: GetCoreUsersRequest): Promise<GetAdminsResponse>;
  getUser(request: GetUserRequest): Promise<UserResponse>;
  endSession(request: ValidateRequest): Promise<Response>;
  readPortalRoles(request: ReadPortalRolesRequest): Promise<ReadPortalRolesResponse>;
  recaptcha(request: RecaptchaRequest): Promise<RecaptchaResponse>;
  reviewAccessRequest(request: ReviewAccessRequest): Promise<Response>;
  sendMagicLink(request: SendMagicLinkRequest): Promise<Response>;
  sendMagicLinkV2(request: SendMagicLinkRequest): Promise<Response>;
  sendVerificationEmail(request: SendVerificationEmailRequest): Promise<Response>;
  validateSession(request: ValidateRequest): Promise<SessionResponse>;
  verifyEmail(request: VerifyEmailRequest): Promise<Response>;
  updateUser(request: UpdateUserRequest): Promise<Response>;
}

export interface LinkingRepository {
  inviteCaseParticipant(
    request: InviteCaseParticipantRequest,
  ): Promise<InviteCaseParticipantResponse>;
  unlinkCaseParticipant(
    request: UnlinkCaseParticipantRequest,
  ): Promise<UnlinkCaseParticipantResponse>;
}

export interface NotificationRepository {
  sendEmail(request: SendEmailRequest__Optional): Promise<SendEmailResponse>;
  notify(request: NotifyRequest__Optional): Promise<NotifyResponse>;
}

export interface VerificationRepository {
  getConfigurations(input: GetConfigurationsRequest): Promise<GetConfigurationsResponse>;
  verify(input: VerificationRequest): Promise<VerificationResponse>;
  uploadFile(input: Omit<UploadFileRequest, 'fields'>): Promise<UploadFileResponse>;
  upsertLookupConfig(
    input: UpsertLookupConfigRequest__Optional,
  ): Promise<UpsertLookupConfigResponse>;
}

interface ExternalRepositories {
  Claim: ClaimRepository;
  Config: ConfigRepository;
  Elasticsearch: ElasticsearchRepository;
  EntityResolution: EntityResolutionRepository;
  Identity: IdentityRepository;
  Linking: LinkingRepository;
  Pubsub: PubsubRepository;
  Notification: NotificationRepository;
  Upload: UploadRepository;
  Verification: VerificationRepository;
}

export type { ExternalRepositories };
