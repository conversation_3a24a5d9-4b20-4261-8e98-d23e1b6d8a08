-- Deploy verification-tables
BEGIN;

SET
  search_path TO public;

SET ROLE
  migration;

CREATE TABLE
  verification_configs (
    id uuid NOT NULL DEFAULT gen_random_uuid (),
    program_id uuid NOT NULL,
    applicant_type_id uuid NOT NULL,
    service_type text NOT NULL,
    config jsonb NOT NULL,
    filepath text DEFAULT NULL,
    status text NOT NULL DEFAULT 'InProgress',
    created_at timestamp NULL DEFAULT now(),
    updated_at timestamp NULL DEFAULT now(),
    uploaded_at timestamp DEFAULT NULL,
    deactivated_at timestamp DEFAULT NULL
  );

ALTER TABLE
  verification_configs
ADD
  CONSTRAINT configs_pkey PRIMARY KEY (id);

ALTER TABLE
  verification_configs
ADD
  CONSTRAINT configs_program_id_fkey FOREIGN KEY (program_id) REFERENCES programs (id);

ALTER TABLE
  verification_configs
ADD
  CONSTRAINT configs_applicant_type_id_fkey FOREIGN KEY (applicant_type_id) REFERENCES applicant_types (id);

CREATE UNIQUE INDEX unique_active_verification_config ON verification_configs (program_id, applicant_type_id, service_type)
WHERE
  (deactivated_at IS NULL);

CREATE INDEX verification_configs_program_id_idx ON verification_configs USING btree (program_id);
CREATE INDEX verification_configs_applicant_type_id_idx ON verification_configs USING btree (applicant_type_id);

COMMIT;